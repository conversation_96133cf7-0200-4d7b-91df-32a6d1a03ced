block result : text!
========

========

block test1 : _lexer_input!
========
1 $ 1
========

block test1_result : _lexer_expected!
=====================
+Number+
1
+BinaryOperator+
$
+Number+
1
=====================


block test_number : _lexer_input!
========
1_2_3_4_5_6_7_8_9_0E+5
========

block test_number_result : _lexer_expected!
=====================
+Number+
1_2_3_4_5_6_7_8_9_0E+5
=====================

block test_suffix_operator : _lexer_input!
========
a++ * b
========

block test_suffix_operator_result : _lexer_expected!
========
+Identifier+
a
+SuffixOperator+
++
+BinaryOperator+
*
+Identifier+
b
========

block test_binary_operator_desc : text !
========
测试二元操作符，由于+是前缀操作符，因此不会被识别为二元操作符
========

block test_binary_operator_wrong : _lexer_input !
========
a +operator+ b
========

block test_binary_operator_wrong_result : _lexer_expected!
========
+Identifier+
a
+PrefixOperator+
+
+Identifier+
operator
+SuffixOperator+
+
+Identifier+
b
========

block error_test : _lexer_input !
========
人 a
========

block error_test_result : _lexer_expected !
========
+Identifier+
a
========

block error_test_error1 : _lexer_error_state !
========
1:1
unexpected_character
lexer_inject
========

