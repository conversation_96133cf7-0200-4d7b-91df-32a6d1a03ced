#include <fstream>
#include <iostream>
#include <string>
#include "../text.h"
#include "../lexer.h"


std::string u32string_to_string(const std::u32string& u32str) {
    char* utf8 = NULL;
    int len = Text::char32ToUtf8(u32str.c_str(), u32str.size(), &utf8);
    if (len <= 0) return "";
    std::string res(utf8);
    free(utf8);
    return res;
}

void generate_error(const std::u32string& message, const SoueceInfo* info) {
    std::string message_str = u32string_to_string(message);
    if (info) {
        printf("Error at line %d, column %d: %s\n", info->line, info->column, message_str.c_str());
    }else{
        printf("Error: %s\n", message_str.c_str());
    }
}

void generate_warning(const std::u32string& message, const SoueceInfo* info) {
    std::string message_str = u32string_to_string(message);
    if (info) {
        printf("Warning at line %d, column %d: %s\n", info->line, info->column, message_str.c_str());
    }else{
        printf("Warning: %s\n", message_str.c_str());
    }
}

struct TestData {
    std::u32string input;
    std::vector<std::u32string> expected_tokens;
    std::vector<TokenType> expected_types;
};

static std::vector<std::u32string> split_by(const std::u32string& input, char32_t split_char) {
    std::vector<std::u32string> res;
    std::u32string current;
    for (auto c : input) {
        if (c == split_char) {
            res.push_back(current);
            current.clear();
        }else{
            current += c;
        }
    }
    if (current.size() > 0) {
        res.push_back(current);
    }
    return res;
}

static TokenType token_type_from_string(const std::u32string& type_string) {
    if (type_string == U"Identifier") return TokenType::Identifier;
    if (type_string == U"RawIdentifier") return TokenType::RawIdentifier;
    if (type_string == U"Number") return TokenType::Number;
    if (type_string == U"String") return TokenType::String;
    if (type_string == U"Char") return TokenType::Char;
    if (type_string == U"LineComment") return TokenType::LineComment;
    if (type_string == U"BlockComment") return TokenType::BlockComment;
    if (type_string == U"BinaryOperator") return TokenType::BinaryOperator;
    if (type_string == U"PrefixOperator") return TokenType::PrefixOperator;
    if (type_string == U"SuffixOperator") return TokenType::SuffixOperator;
    if (type_string == U"Keyword") return TokenType::Keyword;
    if (type_string == U"Punctuation") return TokenType::Punctuation;
    if (type_string == U"Whitespace") return TokenType::Whitespace;
    if (type_string == U"BlockContent") return TokenType::BlockContent;
    return TokenType::Identifier;
}

static std::u32string token_type_to_string(TokenType type) {
    switch (type) {
        case TokenType::Identifier: return U"Identifier";
        case TokenType::RawIdentifier: return U"RawIdentifier";
        case TokenType::Number: return U"Number";
        case TokenType::String: return U"String";
        case TokenType::Char: return U"Char";
        case TokenType::LineComment: return U"LineComment";
        case TokenType::BlockComment: return U"BlockComment";
        case TokenType::BinaryOperator: return U"BinaryOperator";
        case TokenType::PrefixOperator: return U"PrefixOperator";
        case TokenType::SuffixOperator: return U"SuffixOperator";
        case TokenType::Keyword: return U"Keyword";
        case TokenType::Punctuation: return U"Punctuation";
        case TokenType::Whitespace: return U"Whitespace";
        case TokenType::BlockContent: return U"BlockContent";
    }
    return U"Unknown";
}

void parse_expected_tokens(std::vector<std::u32string>& out_tokens,std::vector<TokenType>& out_types, const std::u32string& input) {
    auto lines = split_by(input, '\n');
    enum class ReadState{
        WaitType, WaitValue
    } read_state = ReadState::WaitType;
    std::u32string token_type_string, token_value_string;
    for(auto const& line : lines) {
        if (read_state == ReadState::WaitType) {
            if (line.size() <= 2) continue;
            if (line.front() != U'+' || line.back() != U'+') continue;
            token_type_string = line.substr(1, line.size() - 2);
            read_state = ReadState::WaitValue;
        }else if (read_state == ReadState::WaitValue) {
            if (line.size() <= 2) {
                token_value_string += line;
                continue;
            }else if (line.front() == U'+' && line.back() == U'+') {
                out_tokens.push_back(token_value_string);
                token_value_string.clear();
                out_types.push_back(token_type_from_string(token_type_string));
                token_type_string = line.substr(1, line.size() - 2);
                read_state = ReadState::WaitValue;
            }else{
                token_value_string += line;
            }
        }
    }
    if (token_value_string.size() > 0) {
        out_tokens.push_back(token_value_string);
        out_types.push_back(token_type_from_string(token_type_string));
    }
}

std::list<TestData> read_test_data(const LexerState& lexer) {
    auto tokens = lexer.get_tokens();
    std::list<TestData> res;
    TestData current_data;
    if (tokens.size() <= 1) return res;
    for (auto it = std::next(tokens.begin()); it != tokens.end(); it++) {
        std::shared_ptr<Token> token = *it;
        if (token->type == TokenType::BlockContent){
            auto prev_token = token->prev;
            if (prev_token->type != TokenType::Identifier) continue;
            std::u32string block_type = prev_token->value;
            if (block_type == U"_lexer_input") {
                current_data.input = token->value;
            }else if (block_type == U"_lexer_expected") {
                parse_expected_tokens(current_data.expected_tokens, current_data.expected_types, token->value);
                assert(current_data.input.size() > 0);
                res.push_back(current_data);
                current_data = {};
            }else if (block_type == U"text") {
                // Do nothing
            }else{
                generate_error(U"Unknown block type: " + block_type, nullptr);
            }
        }
    }
    return res;
}

bool run_single_test(const TestData& test_data) {
    LexerState lexer;
    SoueceInfo info{1, 1};
    for (auto c : test_data.input) {
        lexer = lexer.eat_char(c, &info);
        info.column++;
        if (c == '\n') {
            info.line++;
            info.column = 1;
        }
    }
    lexer = lexer.finalize(&info);
    auto tokens = lexer.get_tokens();
    auto output_it = tokens.begin();
    auto expected_value_it = test_data.expected_tokens.begin();
    auto expected_type_it = test_data.expected_types.begin();
    bool has_error = false;
    while (output_it != tokens.end() && expected_value_it != test_data.expected_tokens.end() && expected_type_it != test_data.expected_types.end()) {
        if ((*output_it)->value != *expected_value_it) {
            generate_error(U"Expected token value: " + *expected_value_it + U", but got: " + (*output_it)->value, nullptr);
            has_error = true;
            break;
        }
        if ((*output_it)->type != *expected_type_it) {
            generate_error(U"Expected token type: " + token_type_to_string(*expected_type_it) + U", but got: " + token_type_to_string((*output_it)->type), nullptr);
            has_error = true;
            break;
        }
        output_it++;
        expected_value_it++;
        expected_type_it++;
    }
    if (output_it != tokens.end()) {
        generate_error(U"Extra tokens: " + (*output_it)->value, nullptr);
        has_error = true;
    }
    if (expected_value_it != test_data.expected_tokens.end()) {
        generate_error(U"Missing tokens: " + *expected_value_it, nullptr);
        has_error = true;
    }
    if (has_error) {
        std::cout << "Test failed: " << u32string_to_string(test_data.input) << std::endl;
        std::cout << "Output: " << std::endl;
        for (auto& token : tokens) {
            std::cout << "    " << u32string_to_string(token->value) << ":" << u32string_to_string(token_type_to_string(token->type)) << std::endl;
        }
        std::cout << std::endl;
    }
    return !has_error;
}

bool run_tests(std::list<TestData>& test_data) {
    bool res = true;
    std::cout << "Starting tests..." << std::endl;
    for (auto& data : test_data) {
        std::cout << "Running test: " << u32string_to_string(data.input) << std::endl;
        res &= run_single_test(data);
    }
    return res;
}

int main() {
    std::fstream file("src/llvm-frontend/tests/lexer.lddk");
    std::string content((std::istreambuf_iterator<char>(file)),
        std::istreambuf_iterator<char>());
    char32_t *u32 = NULL;
    int len = Text::utf8ToChar32(content.c_str(), content.size(), &u32);
    if (len <= 0) return -1;
    std::u32string u32str(u32, u32 + len - 1);
    free(u32);
    LexerState lexer;
    SoueceInfo info{1, 1};
    for (auto c : u32str) {
        lexer = lexer.eat_char(c, &info);
        info.column++;
        if (c == '\n') {
            info.line++;
            info.column = 1;
        }
    }
    lexer = lexer.finalize(&info);
    auto test_data = read_test_data(lexer);
    // for (auto& data : test_data) {
    //     std::cout << "Input: " << u32string_to_string(data.input) << std::endl;
    //     std::cout << "Expected: ";
    //     for (auto& token : data.expected_tokens) {
    //         std::cout << u32string_to_string(token) << ", ";
    //     }
    //     std::cout << std::endl;
    // }
    if (run_tests(test_data)) {
        std::cout << "All tests passed" << std::endl;
    }else{
        std::cout << "Some tests failed" << std::endl;
    }
    return 0;
}
